import cv2
import numpy as np
from scipy.ndimage import binary_fill_holes, binary_dilation
from config import Config

class RegionProcessor:
    """区域处理类，负责连通域分析和合并"""
    
    def __init__(self, area_thresh=Config.AREA_THRESH):
        self.area_thresh = area_thresh
        self.config = Config()
    
    def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
        """通过膨胀合并同类房间的连通域"""
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        merged_labels = labels_im.copy()

        # 获取所有非背景标签
        labels = [l for l in np.unique(labels_im) if l != 0]

        # 记录掩码和膨胀掩码
        masks = {l: (merged_labels == l) for l in labels}
        dilated_masks = {
            l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) 
            for l in labels
        }

        # 并查集数据结构实现
        parent = {l: l for l in labels}

        def find(x):
            """查找根节点"""
            while parent[x] != x:
                parent[x] = parent[parent[x]]
                x = parent[x]
            return x

        def union(a, b):
            """合并两个集合"""
            pa, pb = find(a), find(b)
            if pa != pb:
                parent[pb] = pa

        # 两两比较重叠且同类房间的连通域合并
        for i, l1 in enumerate(labels):
            for l2 in labels[i+1:]:
                if room_type_for_label.get(l1) != room_type_for_label.get(l2):
                    continue
                overlap = np.sum(dilated_masks[l1] & masks[l2])
                if overlap > overlap_thresh:
                    union(l1, l2)

        # 重新标记连通域
        label_map = {}
        new_label = 1
        new_room_type_for_label = {}
        new_labels_im = np.zeros_like(labels_im)

        for l in labels:
            root = find(l)
            if root not in label_map:
                label_map[root] = new_label
                new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
                new_label += 1
            label_map[l] = label_map[root]

        for l in labels:
            new_labels_im[merged_labels == l] = label_map[l]

        return new_labels_im, new_room_type_for_label

    def merge_regions(self, labels_im, num_labels, binary):
        """合并相邻的小区域到大区域中"""
        # 构建每个连通区域的掩码和面积信息
        region_masks = {}
        region_areas = {}

        for i in range(1, num_labels):
            mask = (labels_im == i)
            area = np.sum(mask)
            region_masks[i] = mask
            region_areas[i] = area

        # 从大区域向小区域尝试合并
        merged_labels = labels_im.copy()
        # 按面积从大到小排序
        label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)  

        for big_label, big_area in label_list:
            big_mask = region_masks[big_label]
            # 向外膨胀一圈，避免紧贴边界
            big_mask_dilated = binary_dilation(big_mask, iterations=5)  

            for small_label, small_area in label_list:
                if small_label == big_label or small_area == 0:
                    continue
                small_mask = region_masks[small_label]

                # 如果小区域大部分像素都在大区域膨胀范围内，则归并
                overlap = big_mask_dilated & small_mask
                if np.sum(overlap) > Config.INTER_RATIO * np.sum(small_mask):  # 可调阈值：80%
                    merged_labels[merged_labels == small_label] = big_label  # 合并
                    region_masks[big_label] = (merged_labels == big_label)  # 更新大区域mask
                    region_areas[big_label] += region_areas[small_label]
                    region_areas[small_label] = 0  # 被合并掉

        return merged_labels, region_areas, binary

    def process_binary_image(self, image, boxes, classes):
        """处理二值图像用于连通域分析"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        # 在二值图像上绘制家具区域
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:  # 门 - 设置为背景
                cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
            else:  # 家具 - 设置为前景
                cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        # 连通域分析
        num_labels, labels_im = cv2.connectedComponents(binary)
        return self.merge_regions(labels_im, num_labels, binary)
    

    def classify_rooms(self, labels_im, boxes, classes, region_areas):
        """分类房间区域"""
        h, w = labels_im.shape
        room_type_for_label = {}
        max_region_label = max(region_areas, key=region_areas.get)
        max_region_area = region_areas[max_region_label]

        for i in range(1, np.max(labels_im)+1):
            if region_areas.get(i, 0) < self.config.AREA_THRESH:
                continue
                
            mask = (labels_im == i)
            present_classes = set()
            
            # 检测区域内的家具类别
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == self.config.DOOR_CLASS_ID:  # 跳过门
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = self.config.DETECTION_WINDOW_SIZE
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        present_classes.add(cls)
            
            # 根据家具类别确定房间类型
            if (self.config.DINING_TABLE_CLASS_ID in present_classes and
                (self.config.SOFA_GROUNDED_CLASS_ID in present_classes or
                 self.config.SOFA_HIGHLEG_CLASS_ID in present_classes)):
                room_type = self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID]  # 同时有餐桌和沙发的区域视为客厅
            elif self.config.DINING_TABLE_CLASS_ID in present_classes:
                room_type = self.config.ROOM_TYPE_MAP[self.config.DINING_TABLE_CLASS_ID]  # 有餐桌的区域视为餐厅
            else:
                
                room_type = list(self.config.ROOM_TYPE_MAP.values())[-1]
                for cls in present_classes:
                    if cls in self.config.ROOM_TYPE_MAP:
                        room_type = self.config.ROOM_TYPE_MAP[cls]
                        break

            # 特殊处理: 大面积餐厅视为客厅
            if (i == max_region_label and room_type == self.config.ROOM_TYPE_MAP[self.config.DINING_TABLE_CLASS_ID] and
                max_region_area > self.config.LARGE_DINING_ROOM_THRESHOLD):
                room_type = self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID]

            room_type_for_label[i] = room_type

        return room_type_for_label

    def find_nearest_boundaries_around_table(self, image, binary, table_boxes, table_classes, all_boxes=None, all_classes=None):
        """在餐桌周围找到距离餐桌最近的竖着的边界和横着的边界，构成一个类似矩形区域

        Args:
            image: 输入图像
            table_boxes: 餐桌检测框列表
            table_classes: 餐桌类别列表
            all_boxes: 所有检测框列表（包括门）
            all_classes: 所有类别列表（包括门）
        """
        h, w = image.shape[:2]
        dining_areas = []

        # 转换为灰度图用于边界检测
        # gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        # 提取门的位置信息
        door_boxes = []
        if all_boxes is not None and all_classes is not None:
            for box, cls in zip(all_boxes, all_classes):
                if cls == self.config.DOOR_CLASS_ID:
                    door_boxes.append(box)

        for (x1, y1, x2, y2), cls in zip(table_boxes, table_classes):
            if cls != self.config.DINING_TABLE_CLASS_ID:
                continue

            # 餐桌中心和尺寸
            table_center_x = (x1 + x2) // 2
            table_center_y = (y1 + y2) // 2
            table_width = x2 - x1
            table_height = y2 - y1

            # 找到最近的竖直边界（左右），考虑门的阻挡
            nearest_left_boundary = self._find_nearest_vertical_boundary_with_doors(
                binary, table_center_x, table_center_y, table_height, 'left', door_boxes
            )
            nearest_right_boundary = self._find_nearest_vertical_boundary_with_doors(
                binary, table_center_x, table_center_y, table_height, 'right', door_boxes
            )

            # 找到最近的水平边界（上下），考虑门的阻挡
            nearest_top_boundary = self._find_nearest_horizontal_boundary_with_doors(
                binary, table_center_x, table_center_y, table_width, 'top', door_boxes
            )
            nearest_bottom_boundary = self._find_nearest_horizontal_boundary_with_doors(
                binary, table_center_x, table_center_y, table_width, 'bottom', door_boxes
            )

            # 构成矩形区域
            dining_area = {
                'left': max(0, nearest_left_boundary),
                'right': min(w, nearest_right_boundary),
                'top': max(0, nearest_top_boundary),
                'bottom': min(h, nearest_bottom_boundary),
                'table_box': (x1, y1, x2, y2)  # 保存原始餐桌位置
            }

            # 确保矩形区域包含餐桌
            dining_area['left'] = min(dining_area['left'], x1)
            dining_area['right'] = max(dining_area['right'], x2)
            dining_area['top'] = min(dining_area['top'], y1)
            dining_area['bottom'] = max(dining_area['bottom'], y2)

            dining_areas.append(dining_area)

        return dining_areas

    def _find_nearest_vertical_boundary_with_doors(self, binary, center_x, center_y, table_height, direction, door_boxes):
        """找到最近的竖直边界，考虑门的阻挡"""
        h, w = binary.shape

        # 定义搜索范围（餐桌高度范围内的多条线）
        search_lines = []
        search_range = table_height // 2
        for offset in range(-search_range, search_range + 1, max(1, search_range // 3)):
            y = center_y + offset
            if 0 <= y < h:
                search_lines.append(y)

        if direction == 'left':
            # 向左搜索最近的边界
            for x in range(center_x, -1, -1):
                # 检查是否碰到门
                if self._is_door_blocking_vertical_path(x, search_lines, door_boxes):
                    return x
                # 检查是否碰到其他边界
                if self._is_vertical_boundary_at(binary, x, search_lines):
                    return x
            return 0  # 到达图像边界
        else:  # direction == 'right'
            # 向右搜索最近的边界
            for x in range(center_x, w):
                # 检查是否碰到门
                if self._is_door_blocking_vertical_path(x, search_lines, door_boxes):
                    return x
                # 检查是否碰到其他边界
                if self._is_vertical_boundary_at(binary, x, search_lines):
                    return x
            return w  # 到达图像边界

    def _find_nearest_vertical_boundary(self, binary, center_x, center_y, table_height, direction):
        """找到最近的竖直边界（原有方法，保持兼容性）"""
        h, w = binary.shape

        # 定义搜索范围（餐桌高度范围内的多条线）
        search_lines = []
        search_range = table_height // 2
        for offset in range(-search_range, search_range + 1, max(1, search_range // 3)):
            y = center_y + offset
            if 0 <= y < h:
                search_lines.append(y)

        if direction == 'left':
            # 向左搜索最近的边界
            for x in range(center_x, -1, -1):
                if self._is_vertical_boundary_at(binary, x, search_lines):
                    return x
            return 0  # 到达图像边界
        else:  # direction == 'right'
            # 向右搜索最近的边界
            for x in range(center_x, w):
                if self._is_vertical_boundary_at(binary, x, search_lines):
                    return x
            return w  # 到达图像边界

    def _find_nearest_horizontal_boundary_with_doors(self, binary, center_x, center_y, table_width, direction, door_boxes):
        """找到最近的水平边界，考虑门的阻挡"""
        h, w = binary.shape

        # 定义搜索范围（餐桌宽度范围内的多条线）
        search_lines = []
        search_range = table_width // 2
        for offset in range(-search_range, search_range + 1, max(1, search_range // 3)):
            x = center_x + offset
            if 0 <= x < w:
                search_lines.append(x)

        if direction == 'top':
            # 向上搜索最近的边界
            for y in range(center_y, -1, -1):
                # 检查是否碰到门
                if self._is_door_blocking_horizontal_path(y, search_lines, door_boxes):
                    return y
                # 检查是否碰到其他边界
                if self._is_horizontal_boundary_at(binary, y, search_lines):
                    return y
            return 0  # 到达图像边界
        else:  # direction == 'bottom'
            # 向下搜索最近的边界
            for y in range(center_y, h):
                # 检查是否碰到门
                if self._is_door_blocking_horizontal_path(y, search_lines, door_boxes):
                    return y
                # 检查是否碰到其他边界
                if self._is_horizontal_boundary_at(binary, y, search_lines):
                    return y
            return h  # 到达图像边界

    def _find_nearest_horizontal_boundary(self, binary, center_x, center_y, table_width, direction):
        """找到最近的水平边界（原有方法，保持兼容性）"""
        h, w = binary.shape

        # 定义搜索范围（餐桌宽度范围内的多条线）
        search_lines = []
        search_range = table_width // 2
        for offset in range(-search_range, search_range + 1, max(1, search_range // 3)):
            x = center_x + offset
            if 0 <= x < w:
                search_lines.append(x)

        if direction == 'top':
            # 向上搜索最近的边界
            for y in range(center_y, -1, -1):
                if self._is_horizontal_boundary_at(binary, y, search_lines):
                    return y
            return 0  # 到达图像边界
        else:  # direction == 'bottom'
            # 向下搜索最近的边界
            for y in range(center_y, h):
                if self._is_horizontal_boundary_at(binary, y, search_lines):
                    return y
            return h  # 到达图像边界

    def _is_vertical_boundary_at(self, binary, x, search_lines):
        """检查指定x坐标是否为竖直边界"""
        if x < 0 or x >= binary.shape[1]:
            return True

        boundary_count = 0
        total_count = len(search_lines)

        for y in search_lines:
            if binary[y, x] == 0:  # 黑色像素表示边界
                boundary_count += 1

        # 如果超过一定比例的点都是边界，则认为这是一条边界线
        return boundary_count > total_count * 0.00000005

    def _is_horizontal_boundary_at(self, binary, y, search_lines):
        """检查指定y坐标是否为水平边界"""
        if y < 0 or y >= binary.shape[0]:
            return True

        boundary_count = 0
        total_count = len(search_lines)

        for x in search_lines:
            if binary[y, x] == 0:  # 黑色像素表示边界
                boundary_count += 1

        # 如果超过一定比例的点都是边界，则认为这是一条边界线
        return boundary_count > total_count * 0.00000005

    def _is_door_blocking_vertical_path(self, x, search_lines, door_boxes):
        """检查竖直路径是否被门阻挡"""
        if not door_boxes:
            return False

        for door_x1, door_y1, door_x2, door_y2 in door_boxes:
            # 检查x坐标是否在门的范围内
            if door_x1 <= x <= door_x2:
                # 检查搜索线是否与门有重叠
                for y in search_lines:
                    if door_y1 <= y <= door_y2:
                        return True
        return False

    def _is_door_blocking_horizontal_path(self, y, search_lines, door_boxes):
        """检查水平路径是否被门阻挡"""
        if not door_boxes:
            return False

        for door_x1, door_y1, door_x2, door_y2 in door_boxes:
            # 检查y坐标是否在门的范围内
            if door_y1 <= y <= door_y2:
                # 检查搜索线是否与门有重叠
                for x in search_lines:
                    if door_x1 <= x <= door_x2:
                        return True
        return False

    def grow_dining_area_from_table(self, image, binary, table_boxes, table_classes):
        """从餐桌椅四边向外生长，直到碰到边界停止（保留原有方法以兼容性）"""
        h, w = image.shape[:2]
        dining_areas = []

        # # 转换为灰度图用于边界检测
        # gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)            



        for (x1, y1, x2, y2), cls in zip(table_boxes, table_classes):
            if cls != self.config.DINING_TABLE_CLASS_ID:
                continue

            # 餐桌边界
            table_left = x1
            table_right = x2
            table_top = y1
            table_bottom = y2

            # 四个方向的生长距离
            growth_distances = {
                'left': 0,
                'right': 0,
                'top': 0,
                'bottom': 0
            }

            # 向左生长
            for i in range(table_left, 0, -1):
                if self._hit_boundary(binary, i, table_top, table_bottom, 'vertical'):
                    break
                growth_distances['left'] = table_left - i

            # 向右生长
            for i in range(table_right, w):
                if self._hit_boundary(binary, i, table_top, table_bottom, 'vertical'):
                    break
                growth_distances['right'] = i - table_right

            # 向上生长
            for i in range(table_top, 0, -1):
                if self._hit_boundary(binary, table_left, table_right, i, 'horizontal'):
                    break
                growth_distances['top'] = table_top - i

            # 向下生长
            for i in range(table_bottom, h):
                if self._hit_boundary(binary, table_left, table_right, i, 'horizontal'):
                    break
                growth_distances['bottom'] = i - table_bottom

            # 找到最大生长距离的边
            max_growth_edge = max(growth_distances.items(), key=lambda x: x[1])
            max_edge_name = max_growth_edge[0]
            print(max_edge_name)
            # 创建餐厅区域（最大生长的边不生长）
            dining_area = {
                'left': table_left if max_edge_name == 'left' else table_left - growth_distances['left'],
                'right': table_right if max_edge_name == 'right' else table_right + growth_distances['right'],
                'top': table_top if max_edge_name == 'top' else table_top - growth_distances['top'],
                'bottom': table_bottom if max_edge_name == 'bottom' else table_bottom + growth_distances['bottom']
            }

            # 确保不超出图像边界
            dining_area['left'] = max(0, dining_area['left'])
            dining_area['right'] = min(w, dining_area['right'])
            dining_area['top'] = max(0, dining_area['top'])
            dining_area['bottom'] = min(h, dining_area['bottom'])

            dining_areas.append(dining_area)

        return dining_areas
    
    def _hit_boundary(self, binary, x, y_range_start, y_range_end, direction):
        """检测是否碰到边界"""
        h, w = binary.shape
        
        if direction == 'vertical':
            # 垂直方向检测：检测x列中从y_range_start到y_range_end是否有边界
            if x < 0 or x >= w:
                return True
            boundary_pixels = 0
            total_pixels = 0
            for y in range(y_range_start, min(y_range_end, h)):
                if y < 0 or y >= h:
                    continue
                total_pixels += 1
                if binary[y, x] == 0:  # 碰到黑色边界
                    boundary_pixels += 1
            # 如果边界像素超过一定比例，认为碰到边界
            return boundary_pixels > total_pixels * 0.5
        elif direction == 'horizontal':
            # 水平方向检测：检测y行中从x_range_start到x_range_end是否有边界
            # 注意：参数含义 - x是x_range_start, y_range_start是x_range_end, y_range_end是当前y坐标
            if y_range_end < 0 or y_range_end >= h:
                return True
            boundary_pixels = 0
            total_pixels = 0
            for x_coord in range(x, min(y_range_start, w)):
                if x_coord < 0 or x_coord >= w:
                    continue
                total_pixels += 1
                if binary[y_range_end, x_coord] == 0:  # 碰到黑色边界
                    boundary_pixels += 1
            # 如果边界像素超过一定比例，认为碰到边界
            return boundary_pixels > total_pixels * 0.5
        
        return False